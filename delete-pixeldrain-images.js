import { config } from 'dotenv';
config();
import axios from 'axios';

const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  CYAN: '\x1b[36m',
  YELLOW: '\x1b[33m',
  GRAY: '\x1b[90m',
};

/**
 * Get all files from pixeldrain account
 * @param {string} apiKey - Pixeldrain API key
 * @returns {Promise<Array>} Array of file objects
 */
async function getUserFiles(apiKey) {
  try {
    console.info(`${COLORS.CYAN}[INFO] Fetching user files from pixeldrain...${COLORS.RESET}`);

    const response = await axios.get('https://pixeldrain.com/api/user/files', {
      headers: {
        'Authorization': `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`,
      },
    });

    if (response.status !== 200) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    return response.data.files || [];
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to fetch user files: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

/**
 * Delete a file from pixeldrain
 * @param {string} fileId - File ID to delete
 * @param {string} apiKey - Pixeldrain API key
 * @returns {Promise<boolean>} Success status
 */
async function deleteFile(fileId, apiKey) {
  try {
    const response = await axios.delete(`https://pixeldrain.com/api/file/${fileId}`, {
      headers: {
        'Authorization': `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`,
      },
    });

    if (response.status === 200) {
      return true;
    } else {
      console.error(`${COLORS.RED}[ERROR] Delete failed with status ${response.status}: ${response.data?.message || 'Unknown error'}${COLORS.RESET}`);
      return false;
    }
  } catch (error) {
    if (error.response?.status === 404) {
      console.warn(`${COLORS.YELLOW}[WARN] File ${fileId} not found (already deleted?)${COLORS.RESET}`);
      return true; // Consider it successful if already deleted
    } else if (error.response?.status === 403) {
      console.error(`${COLORS.RED}[ERROR] Access denied for file ${fileId} (not your file?)${COLORS.RESET}`);
      return false;
    } else {
      console.error(`${COLORS.RED}[ERROR] Failed to delete file ${fileId}: ${error.message}${COLORS.RESET}`);
      return false;
    }
  }
}

/**
 * Check if filename matches the patterns: cover_<numbers>.jpg or banner_<numbers>.jpg
 * @param {string} filename - Filename to check
 * @returns {boolean} True if matches pattern
 */
function matchesPattern(filename) {
  // Pattern: cover_<numbers>.jpg or banner_<numbers>.jpg (case insensitive)
  const pattern = /^(cover|banner)_\d+\.jpg$/i;
  return pattern.test(filename);
}

/**
 * Main function to delete matching images
 */
async function main() {
  const apiKey = 'a159edab-dfe2-4bcf-8251-ef39dc3c19ef';

  if (!apiKey) {
    console.error(`${COLORS.RED}[ERROR] PIXELDRAIN_API_KEY environment variable is required${COLORS.RESET}`);
    process.exit(1);
  }

  try {
    // Get all user files
    const files = await getUserFiles(apiKey);
    console.info(`${COLORS.CYAN}[INFO] Found ${files.length} total files in account${COLORS.RESET}`);

    // Filter files that match our pattern
    const matchingFiles = files.filter(file => matchesPattern(file.name));

    if (matchingFiles.length === 0) {
      console.info(`${COLORS.GRAY}[INFO] No files matching pattern found${COLORS.RESET}`);
      return;
    }

    console.info(`${COLORS.YELLOW}[INFO] Found ${matchingFiles.length} files matching pattern:${COLORS.RESET}`);
    matchingFiles.forEach(file => {
      console.info(`${COLORS.GRAY}  - ${file.name} (ID: ${file.id})${COLORS.RESET}`);
    });

    // Ask for confirmation
    console.info(`${COLORS.YELLOW}[CONFIRM] Do you want to delete these ${matchingFiles.length} files? (y/N)${COLORS.RESET}`);

    // Wait for user input
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('', resolve);
    });
    rl.close();

    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.info(`${COLORS.GRAY}[INFO] Operation cancelled${COLORS.RESET}`);
      return;
    }

    // Delete files
    let successCount = 0;
    let failCount = 0;

    console.info(`${COLORS.CYAN}[INFO] Starting deletion process...${COLORS.RESET}`);

    for (const file of matchingFiles) {
      console.info(`${COLORS.GRAY}[INFO] Deleting ${file.name}...${COLORS.RESET}`);

      const success = await deleteFile(file.id, apiKey);
      if (success) {
        successCount++;
        console.info(`${COLORS.GREEN}[SUCCESS] Deleted ${file.name}${COLORS.RESET}`);
      } else {
        failCount++;
        console.error(`${COLORS.RED}[FAILED] Could not delete ${file.name}${COLORS.RESET}`);
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    // Summary
    console.info(`${COLORS.CYAN}[SUMMARY] Deletion complete:${COLORS.RESET}`);
    console.info(`${COLORS.GREEN}  - Successfully deleted: ${successCount}${COLORS.RESET}`);
    if (failCount > 0) {
      console.info(`${COLORS.RED}  - Failed to delete: ${failCount}${COLORS.RESET}`);
    }

  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Script execution failed: ${error.message}${COLORS.RESET}`);
    process.exit(1);
  }
}

await main()
