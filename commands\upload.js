/**
 * @file Anime Upload Command for Discord Bot
 * @description This script implements a Discord bot command for uploading, processing, and publishing anime subtitle files.
 * Key features include:
 * - Environment configuration using dotenv
 * - File upload and processing
 * - Torrent downloading
 * - Subtitle file manipulation
 * - Integration with Pixeldrain for file hosting
 * - Anime title parsing and correction
 * - Progress tracking for downloads
 * - Discord webhook integration for logging and file notifications
 * - Error handling and retry mechanisms
 *
 * The command allows users to:
 * 1. Upload subtitle files
 * 2. Optionally upload associated image files
 * 3. Download original anime files via torrent
 * 4. Process and encode the files
 * 5. Publish the processed files (optional)
 */
import eraiTitleFixes from '../_resources/erai_title_fixes.json' assert { type: 'json' };
import titleFixes from '../_resources/title_fixes.json' assert { type: 'json' };
import seasonMappings from '../_resources/subsplease_season_mapping.json' assert { type: 'json' };;
import dotenv from 'dotenv';
dotenv.config();
import * as cp from 'child_process';
import fs from 'fs/promises';
import fetch from 'node-fetch';
import Parser from 'rss-parser';
import WebTorrent from 'webtorrent';
import Anthropic from '@anthropic-ai/sdk';
import { WebhookClient } from 'discord.js';
import { SlashCommandBuilder } from '@discordjs/builders';
import ProgressBar from 'progress';

// Utility function for controlled delays
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Initialize required services and clients
const parser = new Parser();
const webhookLogs = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_LOGS });
const webhookFiles = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_FILES });

/**
 * @type {SlashCommandBuilder}
 * @description Defines the structure of the /upload slash command
 */
export const data = new SlashCommandBuilder()
  .setName('upload')
  .setDescription('🦌')
  .addAttachmentOption((option) => option.setName('file').setDescription('🦌').setRequired(true))
  .addBooleanOption((option) => option.setName('publish').setDescription('🦌').setRequired(true))
  .addAttachmentOption((option) => option.setName('image').setDescription('🦌').setRequired(false));

/**
 * @async
 * @description Handles the execution of the /upload command
 * @param {Object} params - The parameters object
 * @param {Object} params.interaction - The Discord interaction object
 * @param {Object} params.client - The Discord client object
 * @param {Object} params.handler - The command handler object
 * @returns {Promise<void>}
 */
export async function run({ interaction, client, handler }) {
  // User authentication
  if (
    interaction.user.id !== '351006685587963916' &&
    interaction.user.id !== '185126303677153281' &&
    interaction.user.id !== '713383731607371827' &&
    interaction.user.id !== '473595271415070750' &&
    interaction.user.id !== '436629443826548736' &&
    interaction.user.id !== '289843490727133185' &&
    interaction.user.id !== '578648970163912715' &&
    interaction.user.id !== '302781404473982977'
  ) {
    await interaction.reply({
      content: 'Nie możesz użyć tej komendy.',
      ephemeral: true,
    });
    return;
  }
  await interaction.deferReply();

  // Extract command options
  const file = interaction.options.getAttachment('file');
  const boolPublish = interaction.options.getBoolean('publish');
  const imageFile = interaction.options.getAttachment('image');

  try {
    // Parse anime title and episode number from filename
    let animeTitle, episodeNumber;

    // Check if this is a ToonsHub file (case-insensitive)
    if (file.name.toLowerCase().includes('[toonshub]')) {
      // Handle ToonsHub format: [ToonsHub] TITLE S01E03 1080p ...
      const toonsHubMatch = file.name.match(/\[toonshub\]\s*(.+?)\s+s\d+e(\d+)/i);
      if (!toonsHubMatch) {
        throw new Error(`Cannot extract ToonsHub title and episode from ${file.name}`);
      }
      animeTitle = toonsHubMatch[1].trim();
      episodeNumber = toonsHubMatch[2];
    } else {
      // Handle Erai-raws/lycoris.cafe format
      animeTitle = file.name
        .replace('Erai-raws_', '')
        .replace('lycoris.cafe_', '')
        .replace('[lycoris.cafe] ', '')
        .replaceAll('_', ' ')
        .match(/^(.*?)\s-\s[^-]*$/)?.[1];
      episodeNumber = file.name
        .replace('Erai-raws_', '')
        .replace('lycoris.cafe_', '')
        .replace('[lycoris.cafe] ', '')
        .replaceAll('_', ' ')
        .match(/(?<=-\s*)\d+/)?.[0];
    }

    if (!animeTitle || !episodeNumber) {
      throw new Error(`Cannot extract name from ${file.name}`);
    }

    // Apply title fixes for different naming conventions
    let eraiTitle = fixTitleForErai(animeTitle) || animeTitle;
    let fixedTitle = fixTitle(animeTitle);
    if (fixedTitle) {
      animeTitle = fixedTitle;
    }

    if (file) {
      // File processing and upload logic
      await interaction.editReply('[INFO] Checking the file for issues... (this may take up to a minute)');
      let fileName = `[lycoris.cafe] ${animeTitle} - ${episodeNumber}.ass`;
      fileName = fileName.replace('2 5 Jigen no Ririsa', '2.5-Jigen no Ririsa');

      // Fetch and process subtitle file
      const response = await fetch(file.url);
      const buffer = await response.arrayBuffer();
      const textDecoder = new TextDecoder();
      let subtitleData = textDecoder.decode(buffer);

      // Upload subtitle file to Pixeldrain
      const apiKey = process.env.PIXELDRAIN_API_KEY;
      const uploadResult = await uploadFileToPixeldrain(Buffer.from(subtitleData), fileName, apiKey);

      // Save the uploaded file locally
      const savePath = `5upload/subPL/${fileName}`;
      await saveFile(`https://pixeldrain.com/api/file/${uploadResult.id}`, savePath);

      // Handle optional image file upload
      let imageUploadResult = null;
      if (imageFile) {
        await interaction.editReply('[INFO] Uploading image file...');
        const imageResponse = await fetch(imageFile.url);
        const imageBuffer = await imageResponse.arrayBuffer();
        const imageFileName = `[lycoris.cafe] ${animeTitle} - ${episodeNumber}_image${imageFile.name.slice(
          imageFile.name.lastIndexOf('.')
        )}`;
        imageUploadResult = await uploadFileToPixeldrain(Buffer.from(imageBuffer), imageFileName, apiKey);
      }

      // Fetch and download torrent
      await interaction.editReply('[INFO] Fetching torrent to download the original file...');
      const torrentResult = await getValidTorrentLink(eraiTitle, episodeNumber);
      await interaction.editReply('[INFO] Downlaoding the original file...');

      // Determine the appropriate title format for the download based on source
      let downloadTitle;
      if (torrentResult.source === 'toonshub') {
        downloadTitle = `[ToonsHub] ${animeTitle} S01E${episodeNumber.padStart(2, '0')}`;
      } else if (torrentResult.source === 'subsplease') {
        downloadTitle = `[SubsPlease] ${animeTitle} - ${episodeNumber}`;
      } else {
        downloadTitle = `[Erai-raws] ${animeTitle} - ${episodeNumber}`;
      }

      await downloadTorrent(torrentResult.link, downloadTitle);

      // Process the downloaded file
      await interaction.editReply(
        `[INFO] Processing the file: \`${file.name}\`. It will take about 30 minutes. Next updates will be sent to <#1268582276434755634> using webhooks.`
      );
      await executeCommand('node', ['4encode/encode.js'], { stdio: 'inherit' });

      // Prepare and execute upload command
      const uploadArgs = [boolPublish.toString(), uploadResult.id];
      if (imageUploadResult) {
        uploadArgs.push(`https://pixeldrain.com/api/file/${imageUploadResult.id}`);
      }
      await executeCommand('node', ['5upload/upload.js', ...uploadArgs], { stdio: 'inherit' });

      process.exit(0);
    } else {
      await webhookFiles.send({
        content: `No file was uploaded.`,
      });
    }
  } catch (error) {
    console.error(`Error processing file: ${error}`);

    // Clean up directories in case of error
    try {
      console.info('[INFO] Performing cleanup after error...');

      // Use the cleanup utility script
      await executeCommand('node', ['cleanup.js'], { stdio: 'inherit' });

      console.info('[INFO] Cleanup after error completed successfully');
    } catch (cleanupError) {
      console.error(`[ERROR] Failed to clean up after error: ${cleanupError.message}`);
    }

    await webhookLogs.send({
      content: `An error occurred while processing the file.\n\`\`\`${error}\`\`\`\n<@351006685587963916> napraw`,
    });
  }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
  devOnly: true,
  deleted: false,
};

/**
 * @async
 * @description Uploads a file to Pixeldrain with retry mechanism
 * @param {Buffer} buffer - The file buffer to upload
 * @param {string} fileName - The name of the file
 * @param {string} apiKey - The Pixeldrain API key
 * @returns {Promise<Object>} The upload result
 */
async function uploadFileToPixeldrain(buffer, fileName, apiKey) {
  let attempts = 0;
  const maxAttempts = 999;

  while (attempts < maxAttempts) {
    attempts++;
    try {
      const apiUrl = `https://pixeldrain.com/api/file/${fileName}`;
      console.info(`[INFO] Attempt ${attempts} to upload file ${fileName}...`);

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          Authorization: `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`,
        },
        body: buffer,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      } else {
        console.info(`[INFO] Upload successful for: ${fileName}`);
        return await response.json();
      }
    } catch (error) {
      console.error(`[ERROR] Upload attempt ${attempts} encountered an error: ${error}`);
      if (attempts >= maxAttempts) {
        console.error(`[ERROR] Giving up after ${attempts} attempts`);
        throw new Error(`Failed after ${attempts} attempts: ${error}`);
      }
      await sleep(10000); // Wait for 1 minute before retrying
    }
  }
}

/**
 * @description Corrects known title inconsistencies (Erai -> Our Names)
 * @param {string} title - The original title
 * @returns {string|undefined} The corrected title or undefined if no correction is needed
 */
function fixTitle(title) {
  return titleFixes[title];
}

/**
 * @description Adjusts titles for Erai-raws naming convention (Our Names -> Erai)
 * @param {string} title - The original title
 * @returns {string|undefined} The adjusted title or undefined if no adjustment is needed
 */
function fixTitleForErai(title) {
  return eraiTitleFixes[title];
}

/**
 * @description Reverses season mapping for torrent search
 * @param {string} animeTitle - The anime title
 * @param {string} episodeNumber - The episode number
 * @returns {Object} Object with title and episode for torrent search
 */
function reverseSeasonMapping(animeTitle, episodeNumber) {
  const mappings = seasonMappings.mappings || {};

  // Find matching season mapping
  for (const [originalTitle, mapping] of Object.entries(mappings)) {
    if (mapping.seasonTitle === animeTitle) {
      const originalEpisode = (mapping.startEpisode - 1) + parseInt(episodeNumber, 10);
      console.log(`[INFO] Reverse season mapping: "${animeTitle}" ep ${episodeNumber} -> "${originalTitle}" ep ${originalEpisode}`);
      return {
        title: originalTitle,
        episode: originalEpisode.toString().padStart(2, '0')
      };
    }
  }

  return { title: animeTitle, episode: episodeNumber };
}

/**
 * @description Applies forward season mapping to convert original title/episode to season title/episode
 * @param {string} originalTitle - The original anime title
 * @param {number} originalEpisode - The original episode number
 * @returns {Object} Object with season title and episode
 */
function forwardSeasonMapping(originalTitle, originalEpisode) {
  const mappings = seasonMappings.mappings || {};

  // Check if this title has season mapping and if episode is in season range
  if (mappings[originalTitle] && originalEpisode >= mappings[originalTitle].startEpisode) {
    const seasonEpisode = originalEpisode - (mappings[originalTitle].startEpisode - 1);
    console.log(`[INFO] Forward season mapping: "${originalTitle}" ep ${originalEpisode} -> "${mappings[originalTitle].seasonTitle}" ep ${seasonEpisode}`);
    return {
      title: mappings[originalTitle].seasonTitle,
      episode: seasonEpisode
    };
  }

  return { title: originalTitle, episode: originalEpisode };
}

/**
 * @async
 * @description Saves a file from a URL to a local path
 * @param {string} url - The URL of the file to save
 * @param {string} savePath - The local path to save the file
 * @returns {Promise<void>}
 */
async function saveFile(url, savePath) {
  const response = await fetch(url);
  const buffer = await response.buffer();
  await fs.writeFile(savePath, buffer);
  console.log(`File saved to ${savePath}`);
}

function isValidEpisodeFormat(title, episodeNumber) {
  // Convert episode number to string and pad with zeros if needed
  const paddedEpisodeNumber = episodeNumber.toString().padStart(2, '0');

  // Create regex pattern that matches:
  // 1. A space, hyphen, space " - "
  // 2. Followed by the exact episode number
  // 3. Optionally followed by "v2", "v3", etc.
  const pattern = new RegExp(` - ${paddedEpisodeNumber}(?:v\\d+)?\\b`);

  return pattern.test(title);
}

/**
 * @description Validates if a SubsPlease title matches the expected episode format
 * @param {string} title - The SubsPlease title to validate
 * @param {string} episodeNumber - The episode number to match
 * @returns {boolean} True if the title matches the expected episode format
 */
function isValidSubspleaseEpisodeFormat(title, episodeNumber) {
  // Convert episode number to string and pad with zeros if needed
  const paddedEpisodeNumber = episodeNumber.toString().padStart(2, '0');

  // SubsPlease format: [SubsPlease] Title - 13 (1080p) [hash].mkv
  // Create regex pattern that matches " - ##" format
  const episodeRegex = new RegExp(`\\s-\\s${paddedEpisodeNumber}\\s`, 'i');

  return episodeRegex.test(title);
}

/**
 * @description Validates if a ToonsHub title matches the expected episode format
 * @param {string} title - The ToonsHub title to validate
 * @param {string} episodeNumber - The episode number to match
 * @returns {boolean} True if the title matches the expected episode format
 */
function isValidToonsHubEpisodeFormat(title, episodeNumber) {
  // Convert episode number to string and pad with zeros if needed
  const paddedEpisodeNumber = episodeNumber.toString().padStart(2, '0');

  // ToonsHub formats:
  // 1. [ToonsHub] TITLE S01E03 1080p ...
  // 2. Title.Name.S01E03.Episode.Title.REPACK.1080p...ToonsHub
  // Create regex pattern that matches S01E{episodeNumber} or S{season}E{episodeNumber} (case-insensitive)
  const pattern = new RegExp(`s\\d+e${paddedEpisodeNumber}\\b`, 'i');

  return pattern.test(title);
}

/**
 * @description Extracts episode information from a ToonsHub title
 * @param {string} title - The ToonsHub title
 * @returns {Object|null} Object with title and episode info, or null if extraction fails
 */
function extractToonsHubEpisodeInfo(title) {
  // Examples:
  // 1. "[ToonsHub] YAIBA Samurai Legend S01E03 1080p B-Global WEB-DL AAC2.0 H.264 (Shin Samurai-den YAIBA, Multi-Subs)"
  // 2. "Kakushite.Makina-san.S01E11.Thus.Makina.Gets.Covered.in.Burrs.REPACK.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub"

  let titleMatch;

  // Format 1: [ToonsHub] prefix format
  if (title.toLowerCase().includes('[toonshub]')) {
    const withoutPrefix = title.replace(/\[toonshub\]\s*/i, '').trim();
    titleMatch = withoutPrefix.match(/^(.+?)\s+s(\d+)e(\d+)/i);
  }
  // Format 2: Dot-separated format
  else {
    titleMatch = title.match(/^(.+?)\.s(\d+)e(\d+)\./i);
    if (titleMatch) {
      // Replace dots with spaces in the title
      titleMatch[1] = titleMatch[1].replace(/\./g, ' ');
    }
  }

  if (!titleMatch) {
    return null;
  }

  return {
    animeTitle: titleMatch[1].trim(),
    season: titleMatch[2],
    episode: titleMatch[3]
  };
}

// Example usage in your existing function:
function validateTorrentTitle(title, episodeNumber) {
  // Basic validation
  if (!title || !episodeNumber) {
    return false;
  }

  return isValidEpisodeFormat(title, episodeNumber);
}

/**
 * @async
 * @description Fetches a valid torrent link for the given anime and episode
 * @param {string} animeTitle - The title of the anime
 * @param {string} episodeNumber - The episode number
 * @returns {Promise<{link: string, source: string}>} Object with the valid torrent link and source type
 * @throws {Error} If no valid torrent link is found
 */
async function getValidTorrentLink(animeTitle, episodeNumber) {
  const maxAttempts = 3;
  let attempts = 0;

  // Apply reverse season mapping
  const { title: searchTitle, episode: searchEpisode } = reverseSeasonMapping(animeTitle, episodeNumber);

  while (attempts < maxAttempts) {
    attempts++;
    try {
      // First try to find erai-raws releases
      console.log(`[INFO] Attempting to find erai-raws release for ${searchTitle} - ${searchEpisode}`);
      const eraiLink = await getEraiRawsTorrentLink(searchTitle, searchEpisode);
      if (eraiLink) {
        console.log(`[INFO] Found erai-raws release: ${eraiLink}`);
        return { link: eraiLink, source: 'erai-raws' };
      }

      // If no erai-raws release found, try SubsPlease
      console.log(`[INFO] No erai-raws release found, trying SubsPlease for ${searchTitle} - ${searchEpisode}`);
      const subspleaseLink = await getSubsPleaseTorrentLink(searchTitle, searchEpisode);
      if (subspleaseLink) {
        console.log(`[INFO] Found SubsPlease release: ${subspleaseLink}`);
        return { link: subspleaseLink, source: 'subsplease' };
      }

      // If no SubsPlease release found, try ToonsHub as final fallback
      console.log(`[INFO] No SubsPlease release found, trying ToonsHub for ${searchTitle} - ${searchEpisode}`);
      const toonsHubLink = await getToonsHubTorrentLink(searchTitle, searchEpisode);
      if (toonsHubLink) {
        console.log(`[INFO] Found ToonsHub release: ${toonsHubLink}`);
        return { link: toonsHubLink, source: 'toonshub' };
      }

      throw new Error(`No valid torrent link found for ${searchTitle} - ${searchEpisode} from erai-raws, SubsPlease, or ToonsHub`);
    } catch (error) {
      console.error(`[ERROR] Attempt ${attempts} to get torrent link failed: ${error}`);
      if (attempts >= maxAttempts) {
        console.error(`[ERROR] Giving up after ${attempts} attempts`);
        throw new Error(`Failed to get torrent link after ${attempts} attempts: ${error}`);
      }
      await sleep(10000); // Wait for 10 seconds before retrying
    }
  }
}

/**
 * @async
 * @description Fetches a valid erai-raws torrent link for the given anime and episode
 * @param {string} animeTitle - The title of the anime
 * @param {string} episodeNumber - The episode number
 * @returns {Promise<string|null>} The valid torrent link or null if not found
 */
async function getEraiRawsTorrentLink(animeTitle, episodeNumber) {
  try {
    let rssUrl = `https://nyaa.si/?page=rss&q=erai+${encodeURIComponent(animeTitle)}+-+${episodeNumber}+1080&c=1_2&f=0`;

    const feed = await parser.parseURL(rssUrl);
    console.log(`[DEBUG] Found ${feed.items.length} erai-raws items`);

    // Sort items by pubDate in ascending order (oldest first)
    const sortedItems = feed.items.sort((a, b) =>
      new Date(b.pubDate) - new Date(a.pubDate)
    );

    // Helper function to prioritize JA releases over CA releases
    const prioritizeJAOverCA = (items) => {
      // For "To Be Hero X", specifically prioritize JA releases
      if (animeTitle.toLowerCase().includes('to be hero x')) {
        const jaItems = items.filter(item => !item.title.includes('(CA)'));
        const caItems = items.filter(item => item.title.includes('(CA)'));
        console.log(`[DEBUG] To Be Hero X: Found ${jaItems.length} JA releases and ${caItems.length} CA releases`);
        if (jaItems.length > 0) {
          console.log(`[DEBUG] Prioritizing JA release: ${jaItems[0].title}`);
        }
        return [...jaItems, ...caItems]; // JA first, then CA as fallback
      }
      return items; // No change for other anime
    };

    // First try to find the earliest non-EAC3/HEVC title with valid episode format
    const validNonHevcItems = sortedItems.filter(item =>
      isValidEpisodeFormat(item.title, episodeNumber) &&
      !item.title.includes('HEVC') &&
      !item.title.includes('EAC3')
    );

    const prioritizedNonHevcItems = prioritizeJAOverCA(validNonHevcItems);
    if (prioritizedNonHevcItems.length > 0) {
      return prioritizedNonHevcItems[0].link;
    }

    // If no non-EAC3 version found, look for earliest one with EAC3 but still valid episode format
    const validEac3Items = sortedItems.filter(item =>
      isValidEpisodeFormat(item.title, episodeNumber) &&
      !item.title.includes('HEVC')
    );

    const prioritizedEac3Items = prioritizeJAOverCA(validEac3Items);
    if (prioritizedEac3Items.length > 0) {
      return prioritizedEac3Items[0].link;
    }

    return null;
  } catch (error) {
    console.error(`[ERROR] Failed to fetch erai-raws torrent: ${error}`);
    return null;
  }
}

/**
 * @async
 * @description Fetches a valid SubsPlease torrent link for the given anime and episode
 * @param {string} animeTitle - The title of the anime
 * @param {string} episodeNumber - The episode number
 * @returns {Promise<string|null>} The valid torrent link or null if not found
 */
async function getSubsPleaseTorrentLink(animeTitle, episodeNumber) {
  try {
    const rssUrl = `https://nyaa.si/?page=rss&q=subsplease+${encodeURIComponent(animeTitle)}+-+${episodeNumber}+1080&c=1_2&f=0`;

    const feed = await parser.parseURL(rssUrl);
    console.log(`[DEBUG] Found ${feed.items.length} SubsPlease items`);

    // Sort items by pubDate in descending order (newest first)
    const sortedItems = feed.items.sort((a, b) =>
      new Date(b.pubDate) - new Date(a.pubDate)
    );

    // Find SubsPlease releases that match the episode format
    const validItem = sortedItems.find(item => {
      const titleLower = item.title.toLowerCase();
      return (
        titleLower.includes('subsplease') &&
        titleLower.includes('1080p') &&
        isValidSubspleaseEpisodeFormat(item.title, episodeNumber)
      );
    });

    if (validItem) {
      return validItem.link;
    }

    return null;
  } catch (error) {
    console.error(`[ERROR] Failed to fetch SubsPlease torrent: ${error}`);
    return null;
  }
}

/**
 * @async
 * @description Fetches a valid ToonsHub torrent link for the given anime and episode
 * @param {string} animeTitle - The title of the anime
 * @param {string} episodeNumber - The episode number
 * @returns {Promise<string|null>} The valid torrent link or null if not found
 */
async function getToonsHubTorrentLink(animeTitle, episodeNumber) {
  try {
    // Search for ToonsHub releases
    const rssUrl = `https://nyaa.si/?page=rss&q=toonshub+${encodeURIComponent(animeTitle)}+1080&c=1_2&f=0`;

    const feed = await parser.parseURL(rssUrl);
    console.log(`[DEBUG] Found ${feed.items.length} ToonsHub items`);

    // Sort items by pubDate in descending order (newest first)
    const sortedItems = feed.items.sort((a, b) =>
      new Date(b.pubDate) - new Date(a.pubDate)
    );

    // Find ToonsHub releases that match the episode and are H.264 (not H.265) (case-insensitive)
    const validItem = sortedItems.find(item => {
      const titleLower = item.title.toLowerCase();
      return (
        (titleLower.includes('[toonshub]') || titleLower.includes('toonshub')) &&
        titleLower.includes('1080p') &&
        !titleLower.includes('h.265') &&
        isValidToonsHubEpisodeFormat(item.title, episodeNumber)
      );
    });

    if (validItem) {
      return validItem.link;
    }

    return null;
  } catch (error) {
    console.error(`[ERROR] Failed to fetch ToonsHub torrent: ${error}`);
    return null;
  }
}

/**
 * @description Downloads a torrent file
 * @param {string} torrentLink - The link to the torrent file
 * @param {string} torrentTitle - The title of the torrent
 * @returns {Promise<void>}
 */
function downloadTorrent(torrentLink, torrentTitle) {
  return new Promise((resolve, reject) => {
    console.info(`[INFO] Downloading ${torrentTitle}...`);

    const maxAttempts = 99;
    let attempts = 0;

    function attemptDownload() {
      attempts++;
      try {
        const client = new WebTorrent();
        client.add(torrentLink, { path: '0download' }, (torrent) => {
          const bar = createProgressBar(torrent);
          let updateInterval = setInterval(async () => {
            updateProgressBar(bar, torrent);
            if (torrent.done) {
              clearInterval(updateInterval);

              // Rename downloaded file if necessary
              let oldFilename = torrent.files[0].name;
              let newFilename = oldFilename;

              // Check file type and handle renaming
              const isToonsHub = oldFilename.toLowerCase().includes('[toonshub]') ||
                oldFilename.toLowerCase().includes('toonshub') ||
                oldFilename.match(/\.s\d+e\d+\./i);

              const isSubsPlease = oldFilename.toLowerCase().includes('[subsplease]') ||
                oldFilename.toLowerCase().includes('subsplease');

              if (isToonsHub) {
                // For ToonsHub files, keep the original filename
                console.log(`[INFO] ToonsHub file detected, keeping original filename: ${oldFilename}`);
              } else if (isSubsPlease) {
                // Handle SubsPlease files - apply forward season mapping
                let subspleaseMatch = oldFilename.match(/\[subsplease\]\s*(.+?)\s-\s(\d+)/i);
                if (!subspleaseMatch) {
                  reject(new Error('Failed to extract title from SubsPlease file'));
                  return;
                }

                const originalTitle = subspleaseMatch[1].trim();
                const originalEpisode = parseInt(subspleaseMatch[2], 10);

                // Apply forward season mapping to get the season title
                const seasonInfo = forwardSeasonMapping(originalTitle, originalEpisode);

                // Create new filename with season title
                newFilename = oldFilename.replace(
                  `[SubsPlease] ${originalTitle} - ${subspleaseMatch[2]}`,
                  `[SubsPlease] ${seasonInfo.title} - ${seasonInfo.episode.toString().padStart(2, '0')}`
                );

                console.log(`[INFO] SubsPlease file renamed: ${oldFilename} -> ${newFilename}`);
              } else {
                // Handle Erai-raws files (existing logic)
                let titleMatch = oldFilename.match(/(.+)\s-\s(\d{2})/);
                if (!titleMatch) {
                  reject(new Error('Failed to extract title'));
                  return;
                }

                const animeTitle = titleMatch[1].replace('[Erai-raws] ', '');
                let fixedTitle = fixTitle(animeTitle);
                if (fixedTitle) {
                  newFilename = oldFilename.replace(animeTitle, fixedTitle);
                }
              }

              try {
                await fs.rename(`0download/${oldFilename}`, `0download/${newFilename}`);
                console.info(`[INFO] Download completed for: ${torrentTitle}`);
                resolve();
              } catch (renameError) {
                reject(renameError);
              }
            }
          }, 1000);
        });
      } catch (error) {
        console.error(`[ERROR] Download attempt ${attempts} failed: ${error}`);
        if (attempts < maxAttempts) {
          console.info(`[INFO] Retrying download in 30 seconds...`);
          setTimeout(attemptDownload, 10000);
        } else {
          console.error(`[ERROR] Giving up after ${attempts} attempts`);
          reject(new Error(`Failed to download torrent after ${attempts} attempts: ${error}`));
        }
      }
    }

    attemptDownload();
  });
}

/**
 * @description Creates a progress bar for torrent download
 * @param {Object} torrent - The WebTorrent torrent object
 * @returns {ProgressBar} The created progress bar
 */
function createProgressBar(torrent) {
  return new ProgressBar('Downloading [:bar] 0.00% | Speed: 0.00 KB/s | Downloaded: 0 Bytes / 0 Bytes', {
    total: torrent.length,
    width: 40,
    complete: '=',
    incomplete: ' ',
  });
}

/**
 * @description Updates the progress bar with current download status
 * @param {ProgressBar} bar - The progress bar to update
 * @param {Object} torrent - The WebTorrent torrent object
 */
function updateProgressBar(bar, torrent) {
  // Calculate and format progress information
  const percent = (torrent.progress * 100).toFixed(2);
  const downloadSpeed = formatBytes(torrent.downloadSpeed) + '/s';
  const downloaded = formatBytes(torrent.downloaded);
  const total = formatBytes(torrent.length);

  // Update progress bar format and progress
  bar.fmt = `Downloading [:bar] ${percent}% | Speed: ${downloadSpeed} | Downloaded: ${downloaded} / ${total}`;
  bar.update(torrent.progress, { torrent });
}

/**
 * @description Formats bytes into human-readable string
 * @param {number} bytes - The number of bytes to format
 * @returns {string} Formatted string representing the bytes
 */
function formatBytes(bytes) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
}

/**
 * @description Executes a shell command
 * @param {string} command - The command to execute
 * @param {string[]} args - Arguments for the command
 * @param {Object} options - Options for child_process.spawn
 * @returns {Promise<void>}
 */
function executeCommand(command, args, options) {
  return new Promise((resolve, reject) => {
    const childProcess = cp.spawn(command, args, options);

    childProcess.on('error', (err) => {
      reject(err);
    });

    childProcess.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Command failed with exit code ${code}`));
      } else {
        resolve();
      }
    });
  });
}
